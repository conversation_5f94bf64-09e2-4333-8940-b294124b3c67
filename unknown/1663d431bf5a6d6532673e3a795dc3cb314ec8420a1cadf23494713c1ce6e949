# PHP头部警告修复任务

## 问题描述
PHP脚本 `php/voapi签到.php` 出现警告：
```
PHP Warning: Cannot modify header information - headers already sent by (output started at D:\Data\Sync\Log\php\voapi签到.php:165) in D:\Data\Sync\Log\php\voapi签到.php on line 208
```

## 问题原因
- 第165行：`echo "登录账号: $username 中...\n";` 产生输出
- 第208行：`header('Content-Type: text/plain; charset=utf-8');` 尝试设置HTTP头部
- 一旦有输出，就无法再设置HTTP头部

## 解决方案
采用方案3：重构代码结构

### 重构内容
1. **在脚本开头设置HTTP头部**
   - 在第2行后添加 `header('Content-Type: text/plain; charset=utf-8');`

2. **创建输出缓冲系统**
   - 添加全局变量 `$output_buffer = [];`
   - 创建函数 `output_log($message)` 收集所有输出

3. **替换所有echo输出**
   - 将所有 `echo` 替换为 `output_log()`
   - 保持原有输出格式不变

4. **统一输出**
   - 在脚本末尾统一输出所有缓冲内容
   - 移除原第208行的header设置

### 修改的文件
- `php/voapi签到.php`

### 修改的行数
- 第1-3行：添加header设置和输出缓冲系统
- 第177行：登录提示输出
- 第192行：cookie获取提示输出  
- 第201行：响应体cookie获取提示输出
- 第207-208行：登录失败提示输出
- 第211-212行：登录错误提示输出
- 第217行：签到请求提示输出
- 第219行：签到响应输出
- 第221行：签到错误输出
- 第226-232行：统一输出逻辑

## 预期结果
- 消除PHP头部警告
- 保持所有原有功能不变
- 确保字符编码正确显示
- 代码结构更清晰，符合最佳实践

## 测试建议
运行脚本验证：
1. 不再出现header警告
2. 所有输出正常显示
3. 登录和签到功能正常工作
